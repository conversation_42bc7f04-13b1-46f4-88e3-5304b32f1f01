<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电气元器件管理系统</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="lib/element-ui.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="app">
        <el-container class="main-container">
            <!-- 头部 -->
            <el-header class="header">
                <h1>电气元器件管理系统</h1>
                <div class="header-right">
                    <el-menu mode="horizontal" :default-active="activeTab" @select="handleTabSelect" class="nav-menu">
                        <el-menu-item index="entry">数据录入</el-menu-item>
                        <el-menu-item index="export">数据导出</el-menu-item>
                    </el-menu>
                    <el-button type="text" @click="goToAdmin" class="admin-link">数据库管理</el-button>
                </div>
            </el-header>

            <!-- 主体内容 -->
            <el-main class="main-content">
                <!-- 数据录入页面 -->
                <div v-show="activeTab === 'entry'" class="tab-content">
                    <el-card class="entry-card">
                        <div slot="header" class="card-header">
                            <span>元器件数据录入</span>
                        </div>

                        <!-- 批量录入 -->
                        <div>
                            <el-form :model="batchForm" label-width="120px" class="batch-form">
                                <div class="batch-category-header">
                                    <span class="batch-category-title">批量分类设置</span>
                                    <el-button
                                        type="primary"
                                        size="small"
                                        @click="applyBatchCategoryToAll"
                                        :disabled="!batchForm.manufacturer_id || !batchForm.product_group_id || !batchForm.sub_product_group_id">
                                        应用到所有行
                                    </el-button>
                                </div>
                                <el-row :gutter="20">
                                    <el-col :span="8">
                                        <el-form-item label="制造商">
                                            <el-select v-model="batchForm.manufacturer_id" placeholder="选择制造商" @change="onBatchManufacturerChange" clearable>
                                                <el-option v-for="item in manufacturers" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="产品组">
                                            <el-select v-model="batchForm.product_group_id" placeholder="选择产品组" @change="onBatchProductGroupChange" clearable>
                                                <el-option v-for="item in batchProductGroups" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="子产品组">
                                            <el-select v-model="batchForm.sub_product_group_id" placeholder="选择子产品组" clearable>
                                                <el-option v-for="item in batchSubProductGroups" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                            
                            <el-table :data="batchComponents" border class="batch-table" style="width: 100%; margin-top: 20px;">
                                <el-table-column label="序号" width="60" type="index" :index="1"></el-table-column>
                                <el-table-column prop="manufacturer_id" label="制造商" width="150">
                                    <template slot-scope="scope">
                                        <el-select
                                            v-model="scope.row.manufacturer_id"
                                            placeholder="选择制造商"
                                            @change="onRowManufacturerChange(scope.$index, scope.row.manufacturer_id)"
                                            clearable
                                            size="small">
                                            <el-option v-for="item in manufacturers" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                        </el-select>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="product_group_id" label="产品组" width="150">
                                    <template slot-scope="scope">
                                        <el-select
                                            v-model="scope.row.product_group_id"
                                            placeholder="选择产品组"
                                            @change="onRowProductGroupChange(scope.$index, scope.row.product_group_id)"
                                            clearable
                                            size="small">
                                            <el-option v-for="item in scope.row.productGroups || []" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                        </el-select>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="sub_product_group_id" label="子产品组" width="150">
                                    <template slot-scope="scope">
                                        <el-select
                                            v-model="scope.row.sub_product_group_id"
                                            placeholder="选择子产品组"
                                            clearable
                                            size="small">
                                            <el-option v-for="item in scope.row.subProductGroups || []" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                        </el-select>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="model" label="型号" width="180">
                                    <template slot-scope="scope">
                                        <el-input
                                            v-model="scope.row.model"
                                            placeholder="请输入型号（必填）"
                                            maxlength="100"
                                            show-word-limit
                                            size="small">
                                        </el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="description" label="描述" width="200">
                                    <template slot-scope="scope">
                                        <el-input
                                            v-model="scope.row.description"
                                            placeholder="请输入描述（可选）"
                                            maxlength="500"
                                            show-word-limit
                                            size="small">
                                        </el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="notes" label="备注" width="200">
                                    <template slot-scope="scope">
                                        <el-input
                                            v-model="scope.row.notes"
                                            placeholder="请输入备注（可选）"
                                            maxlength="1000"
                                            show-word-limit
                                            size="small">
                                        </el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="100">
                                    <template slot-scope="scope">
                                        <el-button
                                            type="danger"
                                            size="mini"
                                            @click="removeBatchComponent(scope.$index)"
                                            :disabled="batchComponents.length <= 1">
                                            删除
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <div class="batch-actions">
                                <el-button @click="addBatchComponent" icon="el-icon-plus">添加行</el-button>
                                <el-button type="primary" @click="submitBatchComponents" icon="el-icon-upload2">批量提交</el-button>
                                <el-button @click="resetBatchForm" icon="el-icon-refresh">重置</el-button>
                            </div>

                            <div class="batch-info">
                                <div class="info-title">数据录入说明：</div>
                                <ul>
                                    <li>可以先在上方批量选择分类，然后为每条数据单独调整分类</li>
                                    <li>每条数据都需要选择制造商、产品组、子产品组</li>
                                    <li>型号为必填项，描述和备注为可选项</li>
                                    <li>型号最多100个字符，描述最多500个字符，备注最多1000个字符</li>
                                    <li>支持特殊字符，系统会自动处理数据安全性</li>
                                    <li>提交前会进行数据验证，确保数据完整性</li>
                                    <li>使用事务保存，要么全部成功，要么全部失败</li>
                                </ul>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 数据导出页面 -->
                <div v-show="activeTab === 'export'" class="tab-content">
                    <el-row :gutter="20" style="height: calc(100vh - 140px);">
                        <!-- 左侧选择区域 -->
                        <el-col :span="16">
                            <el-card class="export-left-card">
                                <div slot="header" class="card-header">
                                    <span>选择元器件</span>
                                    <el-radio-group v-model="exportMode" size="small">
                                        <el-radio-button label="search">关键词搜索</el-radio-button>
                                        <el-radio-button label="category">分类浏览</el-radio-button>
                                    </el-radio-group>
                                </div>

                                <!-- 搜索模式 -->
                                <div v-show="exportMode === 'search'" class="export-mode-content">
                                    <div class="search-section">
                                        <el-input
                                            v-model="searchKeyword"
                                            placeholder="请输入型号、描述或备注关键词"
                                            @keyup.enter.native="searchComponents"
                                            clearable
                                            class="search-input">
                                            <el-button slot="append" icon="el-icon-search" @click="searchComponents"></el-button>
                                        </el-input>
                                    </div>

                                    <div class="results-section">
                                        <div class="results-header">
                                            <span class="results-count">搜索结果 ({{ searchPagination.total }})</span>
                                            <el-button
                                                v-if="searchResults.length > 0"
                                                type="text"
                                                size="small"
                                                @click="selectAllSearchResults">
                                                全选
                                            </el-button>
                                        </div>
                                        <el-table
                                            ref="searchTable"
                                            :data="searchResults"
                                            border
                                            style="width: 100%;"
                                            max-height="350"
                                            @selection-change="handleSearchSelectionChange">
                                            <el-table-column type="selection" width="55"></el-table-column>
                                            <el-table-column prop="category_path" label="所属分类" width="180" show-overflow-tooltip></el-table-column>
                                            <el-table-column prop="model" label="型号" width="120"></el-table-column>
                                            <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
                                            <el-table-column prop="notes" label="备注" width="120" show-overflow-tooltip></el-table-column>
                                        </el-table>

                                        <!-- 搜索结果分页 -->
                                        <div v-if="searchPagination.totalPages > 1" class="pagination-container">
                                            <el-pagination
                                                @current-change="handleSearchPageChange"
                                                :current-page="searchPagination.currentPage"
                                                :page-size="searchPagination.pageSize"
                                                layout="prev, pager, next, jumper"
                                                :total="searchPagination.total"
                                                small>
                                            </el-pagination>
                                        </div>
                                    </div>
                                </div>

                                <!-- 分类浏览模式 -->
                                <div v-show="exportMode === 'category'" class="export-mode-content">
                                    <el-row :gutter="20" style="height: 100%;">
                                        <el-col :span="10">
                                            <div class="category-section">
                                                <div class="section-title">分类树</div>
                                                <div class="category-tree-container">
                                                    <el-tree
                                                        :data="categoryTree"
                                                        :props="treeProps"
                                                        @node-click="handleCategoryClick"
                                                        node-key="id"
                                                        class="category-tree"
                                                        :expand-on-click-node="false">
                                                        <span class="custom-tree-node" slot-scope="{ node, data }" :class="'level-' + node.level" :data-level="node.level" :data-type="data.type">
                                                            <span class="tree-node-content">
                                                                <span class="tree-node-icon">{{ getNodeIcon(data.type) }}</span>
                                                                <span class="tree-node-text">{{ node.label }}</span>
                                                            </span>
                                                            <span v-if="data.type === 'sub_product_group' && data.componentCount > 0" class="tree-node-count">({{ data.componentCount }})</span>
                                                        </span>
                                                    </el-tree>
                                                </div>
                                            </div>
                                        </el-col>
                                        <el-col :span="14">
                                            <div class="category-components-section">
                                                <div class="results-header">
                                                    <span class="results-count">{{ currentCategoryName || '请选择分类' }} ({{ categoryPagination.total }})</span>
                                                    <el-button
                                                        v-if="categoryComponents.length > 0"
                                                        type="text"
                                                        size="small"
                                                        @click="selectAllCategoryResults">
                                                        全选
                                                    </el-button>
                                                </div>
                                                <el-table
                                                    ref="categoryTable"
                                                    :data="categoryComponents"
                                                    border
                                                    style="width: 100%;"
                                                    max-height="350"
                                                    @selection-change="handleCategorySelectionChange">
                                                    <el-table-column type="selection" width="55"></el-table-column>
                                                    <el-table-column prop="model" label="型号" width="120"></el-table-column>
                                                    <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
                                                    <el-table-column prop="notes" label="备注" width="120" show-overflow-tooltip></el-table-column>
                                                </el-table>

                                                <!-- 分类浏览分页 -->
                                                <div v-if="categoryPagination.totalPages > 1" class="pagination-container">
                                                    <el-pagination
                                                        @current-change="handleCategoryPageChange"
                                                        :current-page="categoryPagination.currentPage"
                                                        :page-size="categoryPagination.pageSize"
                                                        layout="prev, pager, next, jumper"
                                                        :total="categoryPagination.total"
                                                        small>
                                                    </el-pagination>
                                                </div>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                            </el-card>
                        </el-col>

                        <!-- 右侧已选择区域 -->
                        <el-col :span="8">
                            <el-card class="export-right-card">
                                <div slot="header" class="card-header">
                                    <span>已选择 ({{ selectedComponents.length }})</span>
                                    <el-button
                                        type="text"
                                        @click="clearSelected"
                                        :disabled="selectedComponents.length === 0"
                                        size="small">
                                        清空
                                    </el-button>
                                </div>

                                <div class="selected-list">
                                    <el-table
                                        :data="getSelectedPageData()"
                                        border
                                        style="width: 100%;"
                                        max-height="calc(100vh - 350px)"
                                        size="small">
                                        <el-table-column prop="model" label="型号" width="100"></el-table-column>
                                        <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
                                        <el-table-column label="数量" width="100">
                                            <template slot-scope="scope">
                                                <el-input-number
                                                    v-model="scope.row.quantity"
                                                    :min="1"
                                                    :max="999999"
                                                    size="mini"
                                                    controls-position="right"
                                                    style="width: 80px;"
                                                    @change="updateComponentQuantity(scope.row, scope.row.quantity)">
                                                </el-input-number>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="单位" width="100">
                                            <template slot-scope="scope">
                                                <el-input
                                                    v-model="scope.row.unit"
                                                    size="mini"
                                                    placeholder="单位"
                                                    style="width: 80px;"
                                                    @input="updateComponentUnit(scope.row, scope.row.unit)">
                                                </el-input>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="操作" width="60">
                                            <template slot-scope="scope">
                                                <el-button
                                                    type="danger"
                                                    size="mini"
                                                    icon="el-icon-delete"
                                                    @click="removeSelected((selectedPagination.currentPage - 1) * selectedPagination.pageSize + scope.$index)"
                                                    circle>
                                                </el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>

                                    <!-- 已选择列表分页 -->
                                    <div v-if="selectedPagination.totalPages > 1" class="pagination-container">
                                        <el-pagination
                                            @current-change="handleSelectedPageChange"
                                            :current-page="selectedPagination.currentPage"
                                            :page-size="selectedPagination.pageSize"
                                            layout="prev, pager, next"
                                            :total="selectedPagination.total"
                                            small>
                                        </el-pagination>
                                    </div>
                                </div>

                                <div class="export-actions">
                                    <el-button
                                        type="success"
                                        @click="exportSelected"
                                        :disabled="selectedComponents.length === 0"
                                        icon="el-icon-download"
                                        style="width: 100%;">
                                        导出Excel ({{ selectedComponents.length }})
                                    </el-button>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
            </el-main>
        </el-container>
    </div>

    <!-- Vue.js -->
    <script src="lib/vue.min.js"></script>
    <!-- Element UI JS -->
    <script src="lib/element-ui.js"></script>
    <!-- Axios -->
    <script src="lib/axios.min.js"></script>
    <!-- SheetJS -->
    <script src="lib/xlsx.full.min.js"></script>
    <!-- 主应用脚本 -->
    <script src="js/app.js"></script>
</body>
</html>

// Vue应用主文件
new Vue({
    el: '#app',
    data() {
        return {
            // 当前激活的标签页
            activeTab: 'entry',

            // 基础数据
            manufacturers: [],
            productGroups: [],
            subProductGroups: [],
            batchProductGroups: [],
            batchSubProductGroups: [],

            // 单条录入表单
            entryForm: {
                manufacturer_id: '',
                product_group_id: '',
                sub_product_group_id: '',
                model: '',
                description: '',
                notes: ''
            },

            // 批量录入相关
            batchForm: {
                manufacturer_id: '',
                product_group_id: '',
                sub_product_group_id: ''
            },
            batchComponents: [
                {
                    manufacturer_id: '',
                    product_group_id: '',
                    sub_product_group_id: '',
                    model: '',
                    description: '',
                    notes: '',
                    productGroups: [],
                    subProductGroups: []
                }
            ],

            // 导出页面相关
            exportMode: 'search', // 'search' 或 'category'
            searchKeyword: '',
            searchResults: [],

            // 搜索分页相关
            searchPagination: {
                currentPage: 1,
                pageSize: 20,
                total: 0,
                totalPages: 0
            },

            // 已选择的元器件
            selectedComponents: [],

            // 已选择列表分页相关
            selectedPagination: {
                currentPage: 1,
                pageSize: 15,
                total: 0,
                totalPages: 0
            },

            // 分类浏览相关
            categoryTree: [],
            categoryComponents: [],
            currentCategoryName: '',
            currentSubProductGroupId: null,

            // 分类浏览分页相关
            categoryPagination: {
                currentPage: 1,
                pageSize: 20,
                total: 0,
                totalPages: 0
            },

            treeProps: {
                children: 'children',
                label: 'name'
            }
        }
    },
    
    mounted() {
        this.loadManufacturers();
        this.loadCategoryTree();
    },
    
    methods: {
        // 获取节点图标
        getNodeIcon(type) {
            switch(type) {
                case 'manufacturer':
                    return '🏭'; // 制造商图标
                case 'product_group':
                    return '📦'; // 产品组图标
                case 'sub_product_group':
                    return '🔧'; // 子产品组图标
                default:
                    return '';
            }
        },

        // 标签页切换
        handleTabSelect(key) {
            this.activeTab = key;
        },

        // 跳转到数据库管理页面
        goToAdmin() {
            window.open('/admin', '_blank');
        },
        
        // 加载制造商列表
        async loadManufacturers() {
            try {
                const response = await axios.get('/api/manufacturers');
                this.manufacturers = response.data;
            } catch (error) {
                this.$message.error('加载制造商列表失败');
                console.error(error);
            }
        },
        
        // 制造商变化时加载产品组
        async onManufacturerChange(manufacturerId) {
            this.entryForm.product_group_id = '';
            this.entryForm.sub_product_group_id = '';
            this.productGroups = [];
            this.subProductGroups = [];
            
            if (manufacturerId) {
                try {
                    const response = await axios.get(`/api/manufacturers/${manufacturerId}/product_groups`);
                    this.productGroups = response.data;
                } catch (error) {
                    this.$message.error('加载产品组失败');
                    console.error(error);
                }
            }
        },
        
        // 产品组变化时加载子产品组
        async onProductGroupChange(productGroupId) {
            this.entryForm.sub_product_group_id = '';
            this.subProductGroups = [];
            
            if (productGroupId) {
                try {
                    const response = await axios.get(`/api/product_groups/${productGroupId}/sub_product_groups`);
                    this.subProductGroups = response.data;
                } catch (error) {
                    this.$message.error('加载子产品组失败');
                    console.error(error);
                }
            }
        },
        
        // 批量录入 - 制造商变化
        async onBatchManufacturerChange(manufacturerId) {
            this.batchForm.product_group_id = '';
            this.batchForm.sub_product_group_id = '';
            this.batchProductGroups = [];
            this.batchSubProductGroups = [];
            
            if (manufacturerId) {
                try {
                    const response = await axios.get(`/api/manufacturers/${manufacturerId}/product_groups`);
                    this.batchProductGroups = response.data;
                } catch (error) {
                    this.$message.error('加载产品组失败');
                    console.error(error);
                }
            }
        },
        
        // 批量录入 - 产品组变化
        async onBatchProductGroupChange(productGroupId) {
            this.batchForm.sub_product_group_id = '';
            this.batchSubProductGroups = [];
            
            if (productGroupId) {
                try {
                    const response = await axios.get(`/api/product_groups/${productGroupId}/sub_product_groups`);
                    this.batchSubProductGroups = response.data;
                } catch (error) {
                    this.$message.error('加载子产品组失败');
                    console.error(error);
                }
            }
        },
        
        // 添加单个元器件
        async addComponent() {
            if (!this.validateEntryForm()) {
                return;
            }
            
            try {
                const response = await axios.post('/api/components', this.entryForm);
                this.$message.success('元器件添加成功');
                this.resetEntryForm();
            } catch (error) {
                this.$message.error('添加元器件失败: ' + (error.response?.data?.error || error.message));
                console.error(error);
            }
        },
        
        // 验证单条录入表单
        validateEntryForm() {
            if (!this.entryForm.manufacturer_id) {
                this.$message.warning('请选择制造商');
                return false;
            }
            if (!this.entryForm.product_group_id) {
                this.$message.warning('请选择产品组');
                return false;
            }
            if (!this.entryForm.sub_product_group_id) {
                this.$message.warning('请选择子产品组');
                return false;
            }
            if (!this.entryForm.model.trim()) {
                this.$message.warning('请输入型号');
                return false;
            }
            return true;
        },
        
        // 重置单条录入表单
        resetEntryForm() {
            this.entryForm = {
                manufacturer_id: '',
                product_group_id: '',
                sub_product_group_id: '',
                model: '',
                description: '',
                notes: ''
            };
            this.productGroups = [];
            this.subProductGroups = [];
        },
        
        // 添加批量录入行
        addBatchComponent() {
            this.batchComponents.push({
                manufacturer_id: '',
                product_group_id: '',
                sub_product_group_id: '',
                model: '',
                description: '',
                notes: '',
                productGroups: [],
                subProductGroups: []
            });
        },
        
        // 删除批量录入行
        removeBatchComponent(index) {
            if (this.batchComponents.length > 1) {
                this.batchComponents.splice(index, 1);
            }
        },

        // 处理每行制造商变化
        async onRowManufacturerChange(index, manufacturerId) {
            const row = this.batchComponents[index];
            row.product_group_id = '';
            row.sub_product_group_id = '';
            row.productGroups = [];
            row.subProductGroups = [];

            if (manufacturerId) {
                try {
                    const response = await axios.get(`/api/manufacturers/${manufacturerId}/product_groups`);
                    row.productGroups = response.data;
                } catch (error) {
                    this.$message.error('加载产品组失败');
                    console.error(error);
                }
            }
        },

        // 处理每行产品组变化
        async onRowProductGroupChange(index, productGroupId) {
            const row = this.batchComponents[index];
            row.sub_product_group_id = '';
            row.subProductGroups = [];

            if (productGroupId) {
                try {
                    const response = await axios.get(`/api/product_groups/${productGroupId}/sub_product_groups`);
                    row.subProductGroups = response.data;
                } catch (error) {
                    this.$message.error('加载子产品组失败');
                    console.error(error);
                }
            }
        },

        // 批量应用分类到所有行
        async applyBatchCategoryToAll() {
            if (!this.batchForm.manufacturer_id || !this.batchForm.product_group_id || !this.batchForm.sub_product_group_id) {
                this.$message.warning('请先选择完整的分类信息');
                return;
            }

            try {
                // 获取产品组列表
                const productGroupsResponse = await axios.get(`/api/manufacturers/${this.batchForm.manufacturer_id}/product_groups`);
                const productGroups = productGroupsResponse.data;

                // 获取子产品组列表
                const subProductGroupsResponse = await axios.get(`/api/product_groups/${this.batchForm.product_group_id}/sub_product_groups`);
                const subProductGroups = subProductGroupsResponse.data;

                // 应用到所有行
                this.batchComponents.forEach(row => {
                    row.manufacturer_id = this.batchForm.manufacturer_id;
                    row.product_group_id = this.batchForm.product_group_id;
                    row.sub_product_group_id = this.batchForm.sub_product_group_id;
                    row.productGroups = [...productGroups];
                    row.subProductGroups = [...subProductGroups];
                });

                this.$message.success('已将分类应用到所有行');
            } catch (error) {
                this.$message.error('应用分类失败: ' + (error.response?.data?.error || error.message));
                console.error(error);
            }
        },
        
        // 提交批量录入
        async submitBatchComponents() {
            // 过滤出有型号的数据并验证必填字段
            const components = this.batchComponents
                .filter(item => item.model.trim())
                .map(item => ({
                    manufacturer_id: item.manufacturer_id,
                    product_group_id: item.product_group_id,
                    sub_product_group_id: item.sub_product_group_id,
                    model: item.model.trim(),
                    description: item.description.trim(),
                    notes: item.notes.trim()
                }));

            // 验证是否有数据要提交
            if (components.length === 0) {
                this.$message.warning('请至少填写一个元器件的型号');
                return;
            }

            // 验证每条数据的必填字段
            const invalidRows = [];
            components.forEach((component, index) => {
                const errors = [];
                if (!component.manufacturer_id) errors.push('制造商');
                if (!component.product_group_id) errors.push('产品组');
                if (!component.sub_product_group_id) errors.push('子产品组');
                if (!component.model) errors.push('型号');

                if (errors.length > 0) {
                    invalidRows.push(`第 ${index + 1} 行: ${errors.join('、')}不能为空`);
                }
            });

            if (invalidRows.length > 0) {
                this.$alert(invalidRows.join('\n'), '数据验证失败', {
                    type: 'error',
                    customClass: 'batch-error-dialog'
                });
                return;
            }

            // 先进行数据验证
            try {
                const validateResponse = await axios.post('/api/components/batch/validate', { components });
                const validation = validateResponse.data;

                if (validation.invalid > 0) {
                    // 显示验证错误
                    let errorMessage = `发现 ${validation.invalid} 条数据有错误：\n`;
                    validation.results.forEach(result => {
                        if (!result.valid) {
                            errorMessage += `第 ${result.index + 1} 行: ${result.errors.join(', ')}\n`;
                        }
                    });

                    this.$alert(errorMessage, '数据验证失败', {
                        type: 'error',
                        customClass: 'batch-error-dialog'
                    });
                    return;
                }

                // 数据验证通过，询问用户是否确认保存
                const confirmResult = await this.$confirm(
                    `即将保存 ${validation.valid} 条元器件数据，请确认数据是否正确？`,
                    '确认保存',
                    {
                        confirmButtonText: '确定保存',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );

                if (confirmResult !== 'confirm') {
                    return;
                }

            } catch (error) {
                this.$message.error('数据验证失败: ' + (error.response?.data?.error || error.message));
                return;
            }

            // 执行批量保存
            try {
                const response = await axios.post('/api/components/batch', { components });

                if (response.data.success) {
                    this.$message.success(`成功添加 ${response.data.added} 个元器件`);
                    this.resetBatchForm();
                } else {
                    // 部分保存失败，显示详细错误信息
                    let errorMessage = `保存失败，${response.data.failed} 条数据有问题：\n`;
                    response.data.failed_components.forEach(failed => {
                        errorMessage += `第 ${failed.index + 1} 行 (${failed.model}): ${failed.error}\n`;
                    });

                    this.$alert(errorMessage, '保存失败', {
                        type: 'error',
                        customClass: 'batch-error-dialog'
                    });
                    // 不重置表单，让用户可以修改错误的数据
                }

            } catch (error) {
                const errorData = error.response?.data;
                if (errorData && errorData.failed_components) {
                    // 显示详细的失败信息
                    let errorMessage = `保存失败，${errorData.failed} 条数据有问题：\n`;
                    errorData.failed_components.forEach(failed => {
                        errorMessage += `第 ${failed.index + 1} 行 (${failed.model}): ${failed.error}\n`;
                    });

                    this.$alert(errorMessage, '保存失败', {
                        type: 'error',
                        customClass: 'batch-error-dialog'
                    });
                } else {
                    this.$message.error('批量添加失败: ' + (errorData?.error || error.message));
                }
                // 不重置表单，让用户可以修改错误的数据
            }
        },
        
        // 重置批量录入表单
        resetBatchForm() {
            this.batchForm = {
                manufacturer_id: '',
                product_group_id: '',
                sub_product_group_id: ''
            };
            this.batchProductGroups = [];
            this.batchSubProductGroups = [];
            this.batchComponents = [{
                manufacturer_id: '',
                product_group_id: '',
                sub_product_group_id: '',
                model: '',
                description: '',
                notes: '',
                productGroups: [],
                subProductGroups: []
            }];
        },
        
        // 搜索元器件
        async searchComponents(page = 1) {
            if (!this.searchKeyword.trim()) {
                this.$message.warning('请输入搜索关键词');
                return;
            }

            try {
                const response = await axios.get('/api/components/search', {
                    params: {
                        keyword: this.searchKeyword,
                        page: page,
                        page_size: this.searchPagination.pageSize
                    }
                });

                this.searchResults = response.data.data;
                this.searchPagination.currentPage = response.data.page;
                this.searchPagination.total = response.data.total;
                this.searchPagination.totalPages = response.data.total_pages;

                if (this.searchResults.length === 0 && page === 1) {
                    this.$message.info('没有找到匹配的元器件');
                }
            } catch (error) {
                this.$message.error('搜索失败');
                console.error(error);
            }
        },

        // 搜索分页变化
        handleSearchPageChange(page) {
            this.searchComponents(page);
        },
        
        // 处理搜索结果选择变化
        handleSearchSelectionChange(selection) {
            // 添加新选择的元器件到已选择列表
            selection.forEach(item => {
                if (!this.selectedComponents.find(comp => comp.id === item.id)) {
                    // 为新选择的元器件添加默认数量和单位
                    const componentWithDefaults = {
                        ...item,
                        quantity: 1,
                        unit: '个'
                    };
                    this.selectedComponents.push(componentWithDefaults);
                }
            });
            // 更新已选择列表的分页信息
            this.updateSelectedPagination();
        },

        // 处理分类选择变化
        handleCategorySelectionChange(selection) {
            // 添加新选择的元器件到已选择列表
            selection.forEach(item => {
                if (!this.selectedComponents.find(comp => comp.id === item.id)) {
                    // 为新选择的元器件添加默认数量和单位
                    const componentWithDefaults = {
                        ...item,
                        quantity: 1,
                        unit: '个'
                    };
                    this.selectedComponents.push(componentWithDefaults);
                }
            });
            // 更新已选择列表的分页信息
            this.updateSelectedPagination();
        },

        // 更新已选择列表的分页信息
        updateSelectedPagination() {
            this.selectedPagination.total = this.selectedComponents.length;
            this.selectedPagination.totalPages = Math.ceil(this.selectedComponents.length / this.selectedPagination.pageSize);

            // 如果当前页超出了总页数，调整到最后一页
            if (this.selectedPagination.currentPage > this.selectedPagination.totalPages && this.selectedPagination.totalPages > 0) {
                this.selectedPagination.currentPage = this.selectedPagination.totalPages;
            }
        },

        // 移除已选择的元器件
        removeSelected(index) {
            this.selectedComponents.splice(index, 1);
            // 重新计算分页信息
            this.selectedPagination.total = this.selectedComponents.length;
            this.selectedPagination.totalPages = Math.ceil(this.selectedComponents.length / this.selectedPagination.pageSize);

            // 如果当前页没有数据了，回到上一页
            if (this.selectedPagination.currentPage > this.selectedPagination.totalPages && this.selectedPagination.totalPages > 0) {
                this.selectedPagination.currentPage = this.selectedPagination.totalPages;
            }

            // 如果没有数据了，重置到第一页
            if (this.selectedComponents.length === 0) {
                this.selectedPagination.currentPage = 1;
            }
        },

        // 清空已选择的元器件
        clearSelected() {
            this.selectedComponents = [];
            this.selectedPagination.currentPage = 1;
            this.selectedPagination.total = 0;
            this.selectedPagination.totalPages = 0;
        },

        // 更新元器件数量
        updateComponentQuantity(component, quantity) {
            const index = this.selectedComponents.findIndex(comp => comp.id === component.id);
            if (index !== -1) {
                this.selectedComponents[index].quantity = quantity || 1;
            }
        },

        // 更新元器件单位
        updateComponentUnit(component, unit) {
            const index = this.selectedComponents.findIndex(comp => comp.id === component.id);
            if (index !== -1) {
                this.selectedComponents[index].unit = unit || '个';
            }
        },

        // 导出已选择的元器件（使用SheetJS）
        async exportSelected() {
            if (this.selectedComponents.length === 0) {
                this.$message.warning('请先选择要导出的元器件');
                return;
            }

            try {
                const componentIds = this.selectedComponents.map(comp => comp.id);

                // 获取导出数据
                const response = await axios.post('/api/components/export-data', {
                    component_ids: componentIds
                });

                const exportData = response.data.data;

                if (!exportData || exportData.length === 0) {
                    this.$message.error('没有数据可导出');
                    return;
                }

                // 准备Excel数据，包含数量和单位
                const headers = ['制造商(1级分类)', '产品组(2级分类)', '子产品(3级分类)', '型号', '描述', '备注', '数量', '单位'];

                // 构建工作表数据
                const wsData = [headers];

                exportData.forEach(item => {
                    // 从已选择的组件中找到对应的数量和单位
                    const selectedComponent = this.selectedComponents.find(comp => comp.id === item.id);
                    const quantity = selectedComponent ? selectedComponent.quantity : 1;
                    const unit = selectedComponent ? selectedComponent.unit : '个';

                    wsData.push([
                        item.manufacturer_name,
                        item.product_group_name,
                        item.sub_product_group_name,
                        item.model,
                        item.description,
                        item.notes,
                        quantity,
                        unit
                    ]);
                });

                // 创建工作簿和工作表
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(wsData);

                // 设置列宽
                const colWidths = [
                    { wch: 20 }, // 制造商
                    { wch: 20 }, // 产品组
                    { wch: 20 }, // 子产品
                    { wch: 25 }, // 型号
                    { wch: 30 }, // 描述
                    { wch: 30 }, // 备注
                    { wch: 10 }, // 数量
                    { wch: 10 }  // 单位
                ];
                ws['!cols'] = colWidths;

                // 添加工作表到工作簿
                XLSX.utils.book_append_sheet(wb, ws, '元器件清单');

                // 生成文件名
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:T]/g, '').replace(/(\d{8})(\d{6})/, '$1_$2');
                const filename = `元器件清单_${timestamp}.xlsx`;

                // 导出文件
                XLSX.writeFile(wb, filename);

                this.$message.success('导出成功');
            } catch (error) {
                this.$message.error('导出失败');
                console.error(error);
            }
        },

        // 加载分类树（优化版本）
        async loadCategoryTree() {
            try {
                const response = await axios.get('/api/category_tree');
                this.categoryTree = response.data;
            } catch (error) {
                this.$message.error('加载分类树失败');
                console.error(error);
            }
        },

        // 处理分类树节点点击
        async handleCategoryClick(data) {
            if (data.type === 'sub_product_group') {
                this.currentCategoryName = data.name;
                this.currentSubProductGroupId = data.sub_product_group_id;
                this.categoryPagination.currentPage = 1; // 重置分页
                await this.loadCategoryComponents(1);
            } else {
                this.categoryComponents = [];
                this.currentCategoryName = '';
                this.currentSubProductGroupId = null;
                this.categoryPagination.currentPage = 1;
                this.categoryPagination.total = 0;
                this.categoryPagination.totalPages = 0;
            }
        },

        // 加载分类下的元器件
        async loadCategoryComponents(page = 1) {
            if (!this.currentSubProductGroupId) {
                return;
            }

            try {
                const response = await axios.get('/api/components/by_category', {
                    params: {
                        sub_product_group_id: this.currentSubProductGroupId,
                        page: page,
                        page_size: this.categoryPagination.pageSize
                    }
                });

                this.categoryComponents = response.data.data;
                this.categoryPagination.currentPage = response.data.page;
                this.categoryPagination.total = response.data.total;
                this.categoryPagination.totalPages = response.data.total_pages;
            } catch (error) {
                this.$message.error('加载元器件列表失败');
                console.error(error);
            }
        },

        // 分类浏览分页变化
        handleCategoryPageChange(page) {
            this.loadCategoryComponents(page);
        },

        // 全选搜索结果
        selectAllSearchResults() {
            this.$refs.searchTable.toggleAllSelection();
        },

        // 全选分类结果
        selectAllCategoryResults() {
            this.$refs.categoryTable.toggleAllSelection();
        },

        // 已选择分页变化
        handleSelectedPageChange(page) {
            this.selectedPagination.currentPage = page;
        },

        // 计算已选择列表的分页数据
        getSelectedPageData() {
            this.selectedPagination.total = this.selectedComponents.length;
            this.selectedPagination.totalPages = Math.ceil(this.selectedComponents.length / this.selectedPagination.pageSize);

            const start = (this.selectedPagination.currentPage - 1) * this.selectedPagination.pageSize;
            const end = start + this.selectedPagination.pageSize;
            return this.selectedComponents.slice(start, end);
        }
    }
});
